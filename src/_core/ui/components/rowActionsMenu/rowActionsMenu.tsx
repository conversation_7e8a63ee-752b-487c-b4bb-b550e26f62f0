"use client";

import {
  <PERSON>conButton,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { MoreVertical } from "react-feather";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { iconMap } from "@/_ui/icons/iconMap";
import { DataRow, ActionMeta } from "@/_core/utils/crud";

type Props = {
  row: DataRow;
  actions: ActionMeta[];
};

export default function RowActionsMenu({ row, actions }: Props) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter();

  const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <IconButton onClick={handleOpen}>
        <MoreVertical size={20} />
      </IconButton>
      <Menu anchorEl={anchorEl} open={Bo<PERSON>an(anchorEl)} onClose={handleClose}>
        {actions.map((action) => {
          const Icon = iconMap[action.icon];

          // Si es un componente, renderizarlo directamente
          if (action.type === "component" && action.component) {
            const Component = action.component;
            return (
              <MenuItem key={action.label} onClick={handleClose}>
                <ListItemIcon>
                  <Icon size={18} />
                </ListItemIcon>
                <Component row={row} />
              </MenuItem>
            );
          }

          // Lógica existente para navegación y acciones
          const isNavigationAction = action.type === "navigation" || (!action.type && action.href);
          const href = typeof action.href === "function" ? action.href(row) : action.href;

          const handleActionClick = () => {
            if (isNavigationAction && href) {
              router.push(href);
            } else if (action.action) {
              action.action(row);
            }
            handleClose();
          };

          return (
            <MenuItem
              key={action.label}
              onClick={handleActionClick}
            >
              <ListItemIcon>
                <Icon size={18} />
              </ListItemIcon>
              <ListItemText>{action.label}</ListItemText>
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
}
