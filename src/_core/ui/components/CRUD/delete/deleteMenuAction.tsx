"use client";

import React, { useState } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Box,
  Stack
} from "@mui/material";
import { X, AlertTriangle, Delete } from "react-feather";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { EntityKeys } from "@/_lib/utils/entities";
import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { showErrorToast } from "@/_core/ui/components/toaster/toast";
import { DataRow } from "@/_core/utils/crud";

interface DeleteMenuActionProps {
  row: DataRow;
  entityName?: EntityKeys;
  entityLabel?: string;
  onDeleteSuccess?: () => void;
}

export default function DeleteMenuAction({
  row,
  entityName = "asociados",
  entityLabel,
  onDeleteSuccess,
}: DeleteMenuActionProps) {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      await runFormAction(entityName, "delete", {
        params: { id: String(row.id) }
      });
    },
    onSuccess: () => {
      // Invalidar queries relacionadas para refrescar los datos
      queryClient.invalidateQueries({ 
        queryKey: [entityName] 
      });
      setOpen(false);
      onDeleteSuccess?.();
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 
                    error?.message || 
                    "Error al eliminar el elemento";
      showErrorToast(message);
    },
  });

  const handleOpen = (e: React.MouseEvent) => {
    e.stopPropagation(); // Evitar que se cierre el menú
    setOpen(true);
  };
  
  const handleClose = () => setOpen(false);
  
  const handleConfirmDelete = () => {
    deleteMutation.mutate();
  };

  const displayLabel = entityLabel && row[entityLabel] 
    ? String(row[entityLabel]) 
    : "este elemento";

  return (
    <>
      {/* Trigger Text */}
      <Typography
        onClick={handleOpen}
        sx={{ 
          cursor: "pointer",
          width: "100%",
          typography: { xs: "body2", md: "body1", lg: "body1" }
        }}
      >
        Eliminar
      </Typography>

      {/* Confirmation Modal */}
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: 2,
            p: 1
          }
        }}
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  bgcolor: "error.light",
                  color: "error.main"
                }}
              >
                <AlertTriangle size={20} />
              </Box>
              <Typography variant="h6" color="text.primary">
                Confirmar eliminación
              </Typography>
            </Stack>
            <IconButton onClick={handleClose} size="small">
              <X size={20} />
            </IconButton>
          </Stack>
        </DialogTitle>

        <DialogContent>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            ¿Estás seguro de que deseas eliminar "{displayLabel}"?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            color="inherit"
            disabled={deleteMutation.isPending}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
            startIcon={deleteMutation.isPending ? undefined : <Delete size={16} />}
          >
            {deleteMutation.isPending ? "Eliminando..." : "Eliminar"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
