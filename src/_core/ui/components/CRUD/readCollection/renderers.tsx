/**
 * Common renderers for CRUD operations
 * Centralized rendering functions to avoid code duplication
 */

import React from "react";
import Link from "next/link";
import { Badge, Typography } from "@mui/material";
import RowActionsMenu from "@/_core/ui/components/rowActionsMenu/rowActionsMenu";
import DeleteRenderer from "@/_core/ui/components/CRUD/delete/deleteRenderer";
import { toString, toNumber } from "@/_core/utils/typeConversion";
import { ActionMeta, ColumnRenderer } from "@/_core/utils/crud";

/**
 * Default renderers for common data types
 */
export const renderers: Record<string, ColumnRenderer> = {
  link: (value) => {
    const href = toString(value);
    return (
      <a
        href={href}
        className="text-blue-600 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Typography color="link.main" className="hover:underline">
          {href}
        </Typography>
      </a>
    );
  },

  money: (value) => {
    const amount = toNumber(value);
    return <Typography color="text.main">${amount.toFixed(2)}</Typography>;
  },

  integer: (value) => {
    const num = toNumber(value);
    return <Typography color="text.main">{Math.floor(num)}</Typography>;
  },

  actions: (_, row) => {
    const actions = row.__actions as ActionMeta[] | undefined;
    return <RowActionsMenu row={row} actions={actions || []} />;
  },

  navigate: (value, row, _, options) => {
    const basePath = toString(options?.basePath) || "";
    const paramKey = toString(options?.paramKey) || "id";
    const paramValue = toString(row[paramKey]);
    const href = `${basePath}/${paramValue}`;
    const displayValue = toString(value);

    return (
      <Link href={href}>
        <Typography color="link.main" className="hover:underline">
          {displayValue}
        </Typography>
      </Link>
    );
  },

  badge: (value, _row, _col, options) => {
    const bg = toString(options?.bg) || "bg-gray-100";
    const text = toString(options?.text) || "text-gray-800";
    const displayValue = toString(value);

    return (
      <Badge className={`px-2 py-1 rounded-full ${bg} ${text}`}>
        <Typography color="text.main">{displayValue}</Typography>
      </Badge>
    );
  },

  delete: (_, row, _, options) => {
    const entityName = toString(options?.entityName);
    const entityId = toString(row.id);
    const entityLabel = toString(options?.entityLabel ? row[options.entityLabel as string] : undefined);

    if (!entityName || !entityId) {
      return null;
    }

    return (
      <DeleteRenderer
        entityName={entityName as any}
        entityId={entityId}
        entityLabel={entityLabel}
      />
    );
  },

  hidden: () => null, // No se renderiza nada (ni en cabeza ni cuerpo)

  default: (value) => {
    const displayValue = toString(value);
    return <Typography color="text.main">{displayValue}</Typography>;
  },
};
